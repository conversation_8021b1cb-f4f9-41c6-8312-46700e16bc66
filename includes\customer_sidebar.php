<?php
$currentPath = $_SERVER['REQUEST_URI'];
$basePath = getBasePath();

// Normalize current path for comparison
$normalizedCurrentPath = rtrim(parse_url($currentPath, PHP_URL_PATH), '/');
$customerBasePath = $basePath . '/customer';
$navigation = [
    [
        'name' => 'Dashboard',
        'href' => $basePath . '/customer',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7zm0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10M7 17h4" />',
        'description' => 'Overview & quick actions'
    ],
    [
        'name' => 'Book Appointment',
        'href' => $basePath . '/customer/book',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />',
        'description' => 'Schedule new treatments'
    ],
    [
        'name' => 'My Bookings',
        'href' => $basePath . '/customer/bookings',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />',
        'description' => 'View appointment history'
    ],
    [
        'name' => 'Points & Rewards',
        'href' => $basePath . '/customer/rewards',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />',
        'description' => 'Loyalty program & rewards'
    ],
    [
        'name' => 'Profile',
        'href' => $basePath . '/customer/profile',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />',
        'description' => 'Account settings'
    ]
];
?>

<style>
/* Modern Sidebar Styles */
.modern-sidebar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
    padding: 2rem;
    position: sticky;
    top: 6rem;
    max-height: calc(100vh - 8rem);
    overflow-y: auto;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem;
    border-radius: 16px;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    margin-bottom: 0.5rem;
    border: 1px solid transparent;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #49a75c, #2563eb);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.nav-item:hover::before {
    transform: scaleY(1);
}

.nav-item.active {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(37, 99, 235, 0.1));
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.15);
}

.nav-item.active::before {
    transform: scaleY(1);
}

.nav-item:hover {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.05), rgba(37, 99, 235, 0.05));
    border-color: rgba(73, 167, 92, 0.2);
    transform: translateX(4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.nav-icon {
    width: 20px;
    height: 20px;
    margin-right: 1rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.nav-item.active .nav-icon {
    color: #49a75c;
    transform: scale(1.1);
}

.nav-item:hover .nav-icon {
    color: #49a75c;
    transform: scale(1.05);
}

.nav-text {
    flex: 1;
}

.nav-title {
    font-weight: 600;
    font-size: 0.95rem;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.nav-description {
    font-size: 0.8rem;
    color: #64748b;
    line-height: 1.3;
}

.nav-item.active .nav-title {
    color: #49a75c;
}

.nav-item:hover .nav-title {
    color: #49a75c;
}

.stats-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 16px;
    padding: 1.5rem;
    margin-top: 2rem;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
}

.stats-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: #49a75c;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
}

.stat-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-size: 0.875rem;
    color: #64748b;
    flex: 1;
}

.stat-value {
    font-weight: 600;
    font-size: 0.875rem;
    color: #1e293b;
}

.stat-value.primary {
    color: #49a75c;
}

.stat-value.secondary {
    color: #2563eb;
}

.quick-actions {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
}

.quick-action-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.3s ease;
    margin-bottom: 0.75rem;
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.quick-action-btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #49a75c, #3d8b4e);
    color: white;
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: #49a75c;
    border: 1px solid rgba(73, 167, 92, 0.3);
}

.btn-secondary:hover {
    background: rgba(73, 167, 92, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.2);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .modern-sidebar {
        position: static;
        max-height: none;
        margin-bottom: 2rem;
    }
}
</style>

<div class="modern-sidebar">
    <!-- Navigation -->
    <nav class="space-y-1">
        <?php foreach ($navigation as $item):
            // Normalize item href for comparison
            $normalizedItemHref = rtrim($item['href'], '/');

            // Check if this menu item is active
            $isActive = false;

            // Exact match for all pages
            if ($normalizedCurrentPath === $normalizedItemHref) {
                $isActive = true;
            }
            // For non-dashboard items, check sub-pages
            elseif ($normalizedItemHref !== $customerBasePath && strpos($normalizedCurrentPath . '/', $normalizedItemHref . '/') === 0) {
                $isActive = true;
            }
        ?>
            <a href="<?= $item['href'] ?>" class="nav-item <?= $isActive ? 'active' : '' ?>">
                <svg class="nav-icon <?= $isActive ? 'text-redolence-green' : 'text-gray-400' ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <?= $item['icon'] ?>
                </svg>
                <div class="nav-text">
                    <div class="nav-title"><?= $item['name'] ?></div>
                    <div class="nav-description"><?= $item['description'] ?></div>
                </div>
            </a>
        <?php endforeach; ?>
    </nav>

    <!-- Account Summary -->
    <div class="stats-card">
        <h3 class="stats-title">Account Summary</h3>
        <?php
        // Get customer quick stats
        $customerId = $_SESSION['user_id'];
        $upcomingBookings = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE user_id = ? AND date >= CURDATE() AND status IN ('PENDING', 'CONFIRMED')", [$customerId])['count'];
        $totalBookings = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE user_id = ?", [$customerId])['count'];
        $totalSpent = $database->fetch("SELECT SUM(total_amount) as total FROM bookings WHERE user_id = ? AND status = 'COMPLETED'", [$customerId])['total'] ?? 0;
        ?>

        <div class="stat-item">
            <span class="stat-label">Points Balance</span>
            <span class="stat-value primary"><?= number_format($customerPoints['currentPoints']) ?></span>
        </div>

        <div class="stat-item">
            <span class="stat-label">Upcoming Visits</span>
            <span class="stat-value secondary"><?= $upcomingBookings ?></span>
        </div>

        <div class="stat-item">
            <span class="stat-label">Total Visits</span>
            <span class="stat-value"><?= $totalBookings ?></span>
        </div>

        <div class="stat-item">
            <span class="stat-label">Total Spent</span>
            <span class="stat-value primary">TSH <?= number_format($totalSpent, 0) ?></span>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <h3 class="stats-title">Quick Actions</h3>
        
        <a href="<?= $basePath ?>/customer/book" class="quick-action-btn btn-primary">
            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Book Appointment
        </a>

        <a href="<?= $basePath ?>/customer/bookings" class="quick-action-btn btn-secondary">
            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            View Bookings
        </a>
    </div>
</div>