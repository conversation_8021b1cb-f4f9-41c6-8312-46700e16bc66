<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME . ' Admin' : APP_NAME . ' - Medical Admin Panel' ?></title>
    <meta name="description" content="<?= isset($pageDescription) ? $pageDescription : 'Medical admin panel for Redolence Medi Aesthetics - Advanced medical beauty management system' ?>">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?= getBasePath() ?>/includes/redolence_logo.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'redolence-green': '#49a75c',
                        'redolence-blue': '#5894d2',
                        'redolence-gold': '#f4d03f',
                        'redolence-navy': '#1a2332',
                        'redolence-gray': '#f8fafc',
                        primary: {
                            50: '#f0f9f3',
                            100: '#dcf2e3',
                            200: '#bce5cb',
                            300: '#8dd2a8',
                            400: '#5ab67e',
                            500: '#49a75c',
                            600: '#3a8549',
                            700: '#316a3c',
                            800: '#2b5533',
                            900: '#25462c',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        },
                        medical: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#5894d2',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
                        serif: ['Playfair Display', 'ui-serif', 'Georgia'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                        'pulse-medical': 'pulseMedical 2s ease-in-out infinite',
                        'float': 'float 6s ease-in-out infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.95)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        },
                        pulseMedical: {
                            '0%, 100%': { boxShadow: '0 0 20px rgba(73, 167, 92, 0.3)' },
                            '50%': { boxShadow: '0 0 30px rgba(88, 148, 210, 0.5)' },
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        },
                    },
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --background: #ffffff;
            --foreground: #1a2332;
            --primary-green: #49a75c;
            --primary-blue: #5894d2;
            --accent-gold: #f4d03f;
            --deep-navy: #1a2332;
            --soft-gray: #f8fafc;
            --medical-white: #ffffff;
            --shadow-primary: rgba(73, 167, 92, 0.15);
            --shadow-blue: rgba(88, 148, 210, 0.15);
            --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
            --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
        }

        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        html, body {
            max-width: 100vw;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        body {
            background: var(--background);
            color: var(--foreground);
            font-family: 'Inter', ui-sans-serif, system-ui;
            line-height: 1.6;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--soft-gray);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-green);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-blue);
        }

        /* Selection */
        ::selection {
            background: var(--primary-green);
            color: var(--medical-white);
        }

        /* Medical glass effect for headers and cards */
        .medical-glass {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(73, 167, 92, 0.1);
        }

        /* Medical hover effects */
        .medical-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .medical-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px var(--shadow-primary);
        }

        /* Medical admin specific styles */
        .admin-page {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f0f9f3 100%);
            min-height: 100vh;
        }

        .admin-header {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
            backdrop-filter: blur(20px);
            border-bottom: 2px solid rgba(73, 167, 92, 0.1);
        }

        .admin-sidebar {
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            backdrop-filter: blur(15px);
            border-right: 2px solid rgba(73, 167, 92, 0.1);
        }

        /* Medical navigation styles */
        .nav-item-active {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 4px 15px var(--shadow-primary);
        }

        .nav-item-inactive {
            color: var(--deep-navy);
            border: 1px solid transparent;
        }

        .nav-item-inactive:hover {
            background: var(--gradient-soft);
            color: var(--primary-green);
            border-color: rgba(73, 167, 92, 0.2);
        }

        /* Medical notification styles */
        .notification-badge {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            animation: pulse-medical 2s ease-in-out infinite;
        }

        /* Medical button styles */
        .btn-medical-primary {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-medical-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px var(--shadow-primary);
        }

        .btn-medical-secondary {
            background: white;
            color: var(--primary-green);
            border: 2px solid var(--primary-green);
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-medical-secondary:hover {
            background: var(--primary-green);
            color: white;
            transform: translateY(-2px);
        }
    </style>

    <!-- AJAX Reminder Processor -->
    <script src="<?= getBasePath() ?>/assets/js/reminder-processor.js"></script>
</head>
<body class="antialiased min-h-screen admin-page">
    <!-- Medical Admin Header -->
    <header class="admin-header fixed top-0 left-0 right-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo and Title -->
                <div class="flex items-center">
                    <button id="mobile-menu-toggle" class="lg:hidden p-2 rounded-md text-gray-600 hover:text-redolence-green hover:bg-redolence-green/10 focus:outline-none focus:ring-2 focus:ring-redolence-green transition-all duration-300">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                    <div class="ml-4 lg:ml-0 flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-redolence-green to-redolence-blue rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold font-serif text-redolence-green">Redolence Admin</h1>
                            <p class="text-sm text-gray-600">Medical Aesthetics Management</p>
                        </div>
                    </div>
                </div>

                <!-- User Menu -->
                <div class="flex items-center space-x-4">
                    <!-- Notifications -->
                    <div class="relative">
                        <button id="notificationButton" onclick="toggleNotifications()"
                                class="p-2 rounded-md text-gray-600 hover:text-redolence-green hover:bg-redolence-green/10 focus:outline-none focus:ring-2 focus:ring-redolence-green relative transition-colors">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z" />
                            </svg>
                            <span id="notificationCounter" class="notification-badge absolute -top-1 -right-1 h-5 w-5 rounded-full text-white text-xs font-medium flex items-center justify-center hidden">0</span>
                        </button>

                        <!-- Medical Notification Dropdown -->
                        <div id="notificationDropdown" class="hidden absolute right-0 mt-2 w-96 medical-glass rounded-xl shadow-xl border border-redolence-green/20 z-50 max-h-96 overflow-hidden">
                            <!-- Header -->
                            <div class="px-4 py-3 border-b border-redolence-green/10 flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-redolence-navy">Medical Notifications</h3>
                                <div class="flex items-center space-x-2">
                                    <button onclick="markAllAsRead()" class="text-xs text-redolence-green hover:text-redolence-blue transition-colors font-medium">
                                        Mark all read
                                    </button>
                                    <button onclick="openNotificationsPage()" class="text-xs text-gray-500 hover:text-redolence-green transition-colors">
                                        View all
                                    </button>
                                </div>
                            </div>

                            <!-- Category Tabs -->
                            <div class="px-4 py-2 border-b border-redolence-green/10">
                                <div class="flex space-x-1 text-xs">
                                    <button onclick="filterNotifications('all')" class="notification-tab active px-3 py-1 rounded-md bg-redolence-green text-white font-medium">
                                        All <span id="count-all">0</span>
                                    </button>
                                    <button onclick="filterNotifications('BOOKING')" class="notification-tab px-3 py-1 rounded-md text-gray-600 hover:text-redolence-green transition-colors">
                                        Appointments <span id="count-BOOKING">0</span>
                                    </button>
                                    <button onclick="filterNotifications('CUSTOMER')" class="notification-tab px-3 py-1 rounded-md text-gray-600 hover:text-redolence-green transition-colors">
                                        Patients <span id="count-CUSTOMER">0</span>
                                    </button>
                                    <button onclick="filterNotifications('SYSTEM')" class="notification-tab px-3 py-1 rounded-md text-gray-600 hover:text-redolence-green transition-colors">
                                        System <span id="count-SYSTEM">0</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Notifications List -->
                            <div id="notificationsList" class="max-h-64 overflow-y-auto">
                                <div class="p-4 text-center text-gray-500">
                                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-redolence-green mx-auto mb-2"></div>
                                    Loading medical notifications...
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Profile Dropdown -->
                    <div class="relative">
                        <button id="user-menu-toggle" class="flex items-center space-x-3 p-2 rounded-md text-gray-600 hover:text-redolence-green hover:bg-redolence-green/10 focus:outline-none focus:ring-2 focus:ring-redolence-green transition-all duration-300">
                            <div class="h-8 w-8 rounded-full bg-gradient-to-br from-redolence-green to-redolence-blue flex items-center justify-center">
                                <span class="text-sm font-medium text-white">
                                    <?= strtoupper(substr(getCurrentUser()['name'], 0, 2)) ?>
                                </span>
                            </div>
                            <div class="hidden md:block text-left">
                                <p class="text-sm font-medium text-redolence-navy"><?= htmlspecialchars(getCurrentUser()['name']) ?></p>
                                <p class="text-xs text-gray-500">Medical Administrator</p>
                            </div>
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <!-- Medical Dropdown Menu -->
                        <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 medical-glass border border-redolence-green/20 rounded-lg shadow-xl py-2 z-50">
                            <?php $basePath = getBasePath(); ?>
                            <a href="<?= $basePath ?>/admin/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-redolence-green/10 hover:text-redolence-green transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                Profile Settings
                            </a>
                            <a href="<?= $basePath ?>/admin/settings" class="block px-4 py-2 text-sm text-gray-700 hover:bg-redolence-green/10 hover:text-redolence-green transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                System Settings
                            </a>
                            <div class="border-t border-redolence-green/10 my-1"></div>
                            <a href="<?= $basePath ?>/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-redolence-green/10 hover:text-redolence-green transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                                View Medical Website
                            </a>
                            <a href="<?= $basePath ?>/auth/logout.php" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Sidebar Overlay -->
    <div id="mobile-sidebar-overlay" class="hidden fixed inset-0 z-40 lg:hidden">
        <div class="fixed inset-0 bg-black opacity-50"></div>
    </div>

    <!-- Mobile Medical Sidebar -->
    <div id="mobile-sidebar" class="hidden fixed inset-y-0 left-0 z-50 w-64 admin-sidebar lg:hidden">
        <div class="flex items-center justify-between p-4 border-b border-redolence-green/10">
            <h2 class="text-lg font-semibold text-redolence-green">Medical Navigation</h2>
            <button id="mobile-sidebar-close" class="p-2 rounded-md text-gray-600 hover:text-redolence-green hover:bg-redolence-green/10">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <nav class="mt-4">
            <?php include __DIR__ . '/admin_sidebar_nav.php'; ?>
        </nav>
    </div>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.remove('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.remove('hidden');
        });

        document.getElementById('mobile-sidebar-close').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.add('hidden');
        });

        document.getElementById('mobile-sidebar-overlay').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.add('hidden');
        });

        // User menu toggle
        document.getElementById('user-menu-toggle').addEventListener('click', function() {
            const menu = document.getElementById('user-menu');
            menu.classList.toggle('hidden');
        });

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const userMenuToggle = document.getElementById('user-menu-toggle');
            const userMenu = document.getElementById('user-menu');
            
            if (!userMenuToggle.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });
    </script>