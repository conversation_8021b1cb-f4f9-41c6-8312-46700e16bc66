<?php
/**
 * Medical Admin Dashboard
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once __DIR__ . '/../config/app.php';
} catch (Exception $e) {
    die("Medical System Configuration Error: " . $e->getMessage());
}

// Check if auth object exists
if (!isset($auth)) {
    die("Error: Medical authentication system not found. Please check config/app.php");
}

// Require medical admin authentication
$auth->requireRole('ADMIN');

// Get medical dashboard statistics
$stats = [];

// Total patients
$stats['patients'] = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'CUSTOMER'")['count'];

// Total appointments
$stats['appointments'] = $database->fetch("SELECT COUNT(*) as count FROM bookings")['count'];

// Total medical revenue
$stats['revenue'] = $database->fetch("SELECT SUM(total_amount) as total FROM bookings WHERE status = 'COMPLETED'")['total'] ?? 0;

// Pending appointments
$stats['pending_appointments'] = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE status = 'PENDING'")['count'];

// Today's appointments
$stats['today_appointments'] = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE DATE(date) = CURDATE()")['count'];

// This month's revenue
$stats['month_revenue'] = $database->fetch("SELECT SUM(total_amount) as total FROM bookings WHERE status = 'COMPLETED' AND MONTH(date) = MONTH(CURDATE()) AND YEAR(date) = YEAR(CURDATE())")['total'] ?? 0;

// Recent medical appointments
$recentAppointments = $database->fetchAll("
    SELECT
        b.id,
        b.user_id,
        b.service_id,
        b.package_id,
        b.staff_id,
        b.date,
        b.start_time,
        b.end_time,
        b.status,
        b.total_amount,
        b.notes,
        b.created_at,
        b.updated_at,
        u.name as patient_name,
        u.email as patient_email,
        s.name as treatment_name,
        st.name as specialist_name
    FROM bookings b
    LEFT JOIN users u ON b.user_id = u.id
    LEFT JOIN services s ON b.service_id = s.id
    LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
    ORDER BY b.created_at DESC
    LIMIT 8
");

// Monthly medical revenue data for analytics
$monthlyRevenue = $database->fetchAll("
    SELECT 
        DATE_FORMAT(date, '%Y-%m') as month,
        SUM(total_amount) as revenue,
        COUNT(*) as appointments
    FROM bookings 
    WHERE status = 'COMPLETED' 
    AND date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(date, '%Y-%m')
    ORDER BY month
");

// Treatment popularity
$popularTreatments = $database->fetchAll("
    SELECT 
        s.name as treatment_name,
        COUNT(b.id) as booking_count,
        SUM(b.total_amount) as total_revenue
    FROM bookings b
    JOIN services s ON b.service_id = s.id
    WHERE b.status = 'COMPLETED'
    AND b.date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    GROUP BY s.id, s.name
    ORDER BY booking_count DESC
    LIMIT 5
");

$pageTitle = "Medical Admin Dashboard";
include __DIR__ . '/../includes/admin_header.php';
?>

<!-- Medical Dashboard CSS -->
<style>
/* Medical Dashboard Specific Styles */
.medical-stat-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-stat-card:hover::before {
    left: 100%;
}

.medical-stat-card:hover {
    transform: translateY(-5px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 20px 40px var(--shadow-primary);
}

.medical-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-soft);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.medical-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 10px 30px var(--shadow-primary);
}

.medical-appointment-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(15px);
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.medical-appointment-card:hover {
    transform: translateX(5px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 10px 25px var(--shadow-primary);
}

.medical-quick-action {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-quick-action::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-soft);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.medical-quick-action:hover::after {
    opacity: 1;
}

.medical-quick-action:hover {
    transform: translateY(-8px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 25px 50px var(--shadow-primary);
}

.medical-quick-action > * {
    position: relative;
    z-index: 2;
}

.status-badge-pending {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-badge-confirmed {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    color: #059669;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-badge-completed {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(73, 167, 92, 0.05));
    color: var(--primary-green);
    border: 1px solid rgba(73, 167, 92, 0.3);
}

.status-badge-cancelled {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.medical-chart-container {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    padding: 2rem;
}

@media (max-width: 768px) {
    .medical-stat-card {
        padding: 1.5rem;
        border-radius: 16px;
    }
    
    .medical-icon {
        width: 50px;
        height: 50px;
    }
    
    .medical-quick-action {
        padding: 1.5rem;
        border-radius: 16px;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    
                    <!-- Medical Dashboard Header -->
                    <div class="mb-8">
                        <div class="medical-glass border border-redolence-green/20 rounded-2xl p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h1 class="text-3xl font-bold text-redolence-navy">
                                        Medical Dashboard
                                        <span class="text-redolence-green">Overview</span>
                                    </h1>
                                    <p class="text-gray-600 mt-2">Advanced medical aesthetics management system</p>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="text-right">
                                        <div class="text-sm text-gray-500">System Status</div>
                                        <div class="flex items-center text-redolence-green font-semibold">
                                            <div class="w-2 h-2 bg-redolence-green rounded-full mr-2 animate-pulse"></div>
                                            All Systems Operational
                                        </div>
                                    </div>
                                    <div class="w-12 h-12 bg-gradient-to-br from-redolence-green to-redolence-blue rounded-full flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Statistics Overview -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                        <!-- Total Patients -->
                        <div class="medical-stat-card p-6">
                            <div class="flex items-center">
                                <div class="medical-icon">
                                    <svg class="h-7 w-7 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                    </svg>
                                </div>
                                <div class="ml-5 flex-1">
                                    <div class="text-sm font-medium text-gray-600">Total Patients</div>
                                    <div class="text-3xl font-bold text-redolence-green" id="total-patients"><?= number_format($stats['patients']) ?></div>
                                    <div class="text-xs text-gray-500 mt-1">Registered patients</div>
                                </div>
                            </div>
                        </div>

                        <!-- Total Appointments -->
                        <div class="medical-stat-card p-6">
                            <div class="flex items-center">
                                <div class="medical-icon">
                                    <svg class="h-7 w-7 text-redolence-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div class="ml-5 flex-1">
                                    <div class="text-sm font-medium text-gray-600">Total Appointments</div>
                                    <div class="text-3xl font-bold text-redolence-blue" id="total-appointments"><?= number_format($stats['appointments']) ?></div>
                                    <div class="text-xs text-gray-500 mt-1">All time bookings</div>
                                </div>
                            </div>
                        </div>

                        <!-- Medical Revenue -->
                        <div class="medical-stat-card p-6">
                            <div class="flex items-center">
                                <div class="medical-icon">
                                    <svg class="h-7 w-7 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                    </svg>
                                </div>
                                <div class="ml-5 flex-1">
                                    <div class="text-sm font-medium text-gray-600">Total Revenue</div>
                                    <div class="text-3xl font-bold text-redolence-green" id="total-revenue"><?= formatCurrency($stats['revenue']) ?></div>
                                    <div class="text-xs text-gray-500 mt-1">Medical treatments</div>
                                </div>
                            </div>
                        </div>

                        <!-- Pending Appointments -->
                        <div class="medical-stat-card p-6">
                            <div class="flex items-center">
                                <div class="medical-icon">
                                    <svg class="h-7 w-7 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="ml-5 flex-1">
                                    <div class="text-sm font-medium text-gray-600">Pending Appointments</div>
                                    <div class="text-3xl font-bold text-yellow-600" id="pending-appointments"><?= number_format($stats['pending_appointments']) ?></div>
                                    <div class="text-xs text-gray-500 mt-1">Awaiting confirmation</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Today's Overview -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        <!-- Today's Appointments -->
                        <div class="medical-chart-container">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-bold text-redolence-navy">Today's Schedule</h3>
                                <span class="bg-redolence-green/10 text-redolence-green px-3 py-1 rounded-full text-sm font-semibold">
                                    <?= $stats['today_appointments'] ?> Appointments
                                </span>
                            </div>
                            <div class="text-center py-8">
                                <div class="w-24 h-24 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-12 h-12 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div class="text-4xl font-bold text-redolence-green mb-2"><?= $stats['today_appointments'] ?></div>
                                <div class="text-gray-600">Scheduled for today</div>
                                <a href="<?= getBasePath() ?>/admin/bookings" class="inline-flex items-center mt-4 text-redolence-green hover:text-redolence-blue transition-colors font-semibold">
                                    View Schedule
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>

                        <!-- This Month's Revenue -->
                        <div class="medical-chart-container">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-bold text-redolence-navy">Monthly Revenue</h3>
                                <span class="bg-redolence-blue/10 text-redolence-blue px-3 py-1 rounded-full text-sm font-semibold">
                                    <?= date('F Y') ?>
                                </span>
                            </div>
                            <div class="text-center py-8">
                                <div class="w-24 h-24 bg-gradient-to-br from-redolence-blue/20 to-redolence-green/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-12 h-12 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                                <div class="text-4xl font-bold text-redolence-blue mb-2"><?= formatCurrency($stats['month_revenue']) ?></div>
                                <div class="text-gray-600">This month's earnings</div>
                                <a href="<?= getBasePath() ?>/admin/earnings" class="inline-flex items-center mt-4 text-redolence-blue hover:text-redolence-green transition-colors font-semibold">
                                    View Reports
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Medical Appointments -->
                    <div class="medical-glass border border-redolence-green/20 rounded-2xl mb-8 overflow-hidden">
                        <div class="px-6 py-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-xl font-bold text-redolence-navy">Recent Medical Appointments</h3>
                                    <p class="text-gray-600 mt-1">Latest patient bookings and treatment schedules</p>
                                </div>
                                <a href="<?= getBasePath() ?>/admin/bookings" class="btn-medical-primary">
                                    View All Appointments
                                </a>
                            </div>
                        </div>
                        
                        <div class="space-y-1">
                            <?php foreach ($recentAppointments as $appointment): ?>
                                <div class="medical-appointment-card mx-6 mb-4 p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-4">
                                            <div class="w-12 h-12 bg-gradient-to-br from-redolence-green to-redolence-blue rounded-full flex items-center justify-center">
                                                <span class="text-sm font-bold text-white">
                                                    <?= strtoupper(substr($appointment['patient_name'], 0, 2)) ?>
                                                </span>
                                            </div>
                                            <div>
                                                <div class="font-semibold text-redolence-navy">
                                                    <?= htmlspecialchars($appointment['patient_name']) ?>
                                                </div>
                                                <div class="text-sm text-gray-600">
                                                    <?= htmlspecialchars($appointment['treatment_name'] ?? 'Medical Package') ?>
                                                    <?php if ($appointment['specialist_name']): ?>
                                                        • Dr. <?= htmlspecialchars($appointment['specialist_name']) ?>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    <?= htmlspecialchars($appointment['patient_email']) ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-6">
                                            <div class="text-right">
                                                <div class="font-semibold text-redolence-navy">
                                                    <?= formatDate($appointment['date']) ?>
                                                </div>
                                                <div class="text-sm text-gray-600">
                                                    <?= formatTime($appointment['start_time']) ?>
                                                </div>
                                                <div class="text-sm font-bold text-redolence-green">
                                                    <?= formatCurrency($appointment['total_amount']) ?>
                                                </div>
                                            </div>
                                            <span class="status-badge-<?= strtolower($appointment['status']) ?> px-3 py-1 rounded-full text-xs font-semibold">
                                                <?= ucfirst(strtolower($appointment['status'])) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php if (empty($recentAppointments)): ?>
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Recent Appointments</h3>
                                <p class="text-gray-600">No medical appointments have been scheduled yet.</p>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Medical Quick Actions -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                        <a href="<?= getBasePath() ?>/admin/bookings" class="medical-quick-action p-6 block">
                            <div class="flex items-center">
                                <div class="medical-icon">
                                    <svg class="h-7 w-7 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-bold text-redolence-navy">Manage Appointments</h3>
                                    <p class="text-sm text-gray-600 mt-1">Schedule and manage patient appointments</p>
                                </div>
                            </div>
                        </a>

                        <a href="<?= getBasePath() ?>/admin/services" class="medical-quick-action p-6 block">
                            <div class="flex items-center">
                                <div class="medical-icon">
                                    <svg class="h-7 w-7 text-redolence-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-bold text-redolence-navy">Medical Treatments</h3>
                                    <p class="text-sm text-gray-600 mt-1">Manage aesthetic procedures and services</p>
                                </div>
                            </div>
                        </a>

                        <a href="<?= getBasePath() ?>/admin/customers" class="medical-quick-action p-6 block">
                            <div class="flex items-center">
                                <div class="medical-icon">
                                    <svg class="h-7 w-7 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-bold text-redolence-navy">Patient Management</h3>
                                    <p class="text-sm text-gray-600 mt-1">View and manage patient records</p>
                                </div>
                            </div>
                        </a>

                        <a href="<?= getBasePath() ?>/admin/staff" class="medical-quick-action p-6 block">
                            <div class="flex items-center">
                                <div class="medical-icon">
                                    <svg class="h-7 w-7 text-redolence-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-bold text-redolence-navy">Medical Staff</h3>
                                    <p class="text-sm text-gray-600 mt-1">Manage medical professionals and specialists</p>
                                </div>
                            </div>
                        </a>

                        <a href="<?= getBasePath() ?>/admin/earnings" class="medical-quick-action p-6 block">
                            <div class="flex items-center">
                                <div class="medical-icon">
                                    <svg class="h-7 w-7 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-bold text-redolence-navy">Revenue Analytics</h3>
                                    <p class="text-sm text-gray-600 mt-1">View financial reports and analytics</p>
                                </div>
                            </div>
                        </a>

                        <a href="<?= getBasePath() ?>/admin/settings" class="medical-quick-action p-6 block">
                            <div class="flex items-center">
                                <div class="medical-icon">
                                    <svg class="h-7 w-7 text-redolence-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-bold text-redolence-navy">System Settings</h3>
                                    <p class="text-sm text-gray-600 mt-1">Configure medical system settings</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>