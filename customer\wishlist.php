<?php
/**
 * Customer Wishlist Page
 * Displays saved services and packages
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/wishlist_functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . getBasePath() . '/auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$customerId = $_SESSION['user_id'];

// Pagination
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 12;
$offset = ($page - 1) * $limit;

// Get wishlist items
$wishlistItems = getUserWishlist($customerId, $limit, $offset);
$totalItems = getWishlistCount($customerId);
$totalPages = ceil($totalItems / $limit);

$pageTitle = "My Wishlist";
include __DIR__ . '/../includes/customer_header.php';
?>

                    <!-- Wishlist Content -->
                        <!-- Header -->
                        <div class="mb-8">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h1 class="text-3xl font-bold text-gray-900">My Wishlist</h1>
                                    <p class="mt-2 text-gray-600">
                                        <?= $totalItems ?> saved item<?= $totalItems !== 1 ? 's' : '' ?>
                                    </p>
                                </div>
                                <?php if ($totalItems > 0): ?>
                                    <button onclick="clearWishlist()" 
                                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors">
                                        <i class="fas fa-trash mr-2"></i>
                                        Clear All
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <?php if (empty($wishlistItems)): ?>
                            <!-- Empty State -->
                            <div class="text-center py-16">
                                <div class="w-24 h-24 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <i class="fas fa-heart text-salon-gold text-4xl"></i>
                                </div>
                                <h2 class="text-2xl font-bold text-white mb-4">Your wishlist is empty</h2>
                                <p class="text-gray-300 mb-8 max-w-md mx-auto">
                                    Start adding your favorite services and packages to your wishlist for easy booking later.
                                </p>
                                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                    <a href="<?= getBasePath() ?>/services" 
                                       class="inline-flex items-center justify-center px-6 py-3 bg-salon-gold text-black font-semibold rounded-lg hover:bg-yellow-500 transition-colors">
                                        <i class="fas fa-search mr-2"></i>
                                        Browse Services
                                    </a>
                                    <a href="<?= getBasePath() ?>/packages" 
                                       class="inline-flex items-center justify-center px-6 py-3 border border-salon-gold text-salon-gold font-semibold rounded-lg hover:bg-salon-gold hover:text-black transition-colors">
                                        <i class="fas fa-gift mr-2"></i>
                                        View Packages
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Wishlist Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                                <?php foreach ($wishlistItems as $item): ?>
                                    <div class="bg-secondary-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] group border border-secondary-700 hover:border-salon-gold/30">
                                        <!-- Item Image -->
                                        <div class="relative bg-secondary-700 overflow-hidden">
                                            <?php if ($item['item_image']): ?>
                                                <?php
                                                // Check if image is a URL or uploaded file
                                                $imageSrc = filter_var($item['item_image'], FILTER_VALIDATE_URL)
                                                    ? $item['item_image']
                                                    : getBasePath() . '/uploads/' . $item['item_image'];
                                                ?>
                                                <img src="<?= htmlspecialchars($imageSrc) ?>"
                                                     alt="<?= htmlspecialchars($item['item_name']) ?>"
                                                     class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300">
                                            <?php else: ?>
                                                <div class="w-full h-48 bg-gradient-to-br from-salon-gold/20 to-secondary-700 flex items-center justify-center">
                                                    <i class="fas fa-<?= $item['item_type'] === 'service' ? 'cut' : 'gift' ?> text-salon-gold/50 text-4xl"></i>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <!-- Remove from Wishlist Button -->
                                            <button onclick="removeFromWishlist('<?= $item['item_type'] ?>', '<?= $item['item_id'] ?>')"
                                                    class="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white p-2 rounded-full transition-colors opacity-0 group-hover:opacity-100">
                                                <i class="fas fa-times text-sm"></i>
                                            </button>
                                            
                                            <!-- Price Badge -->
                                            <div class="absolute top-2 left-2 bg-salon-gold text-black px-2 py-1 rounded-full text-sm font-bold">
                                                <?= formatCurrency($item['item_price']) ?>
                                            </div>
                                            
                                            <!-- Type Badge -->
                                            <div class="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                                <?= ucfirst($item['item_type']) ?>
                                            </div>
                                        </div>

                                        <!-- Item Content -->
                                        <div class="p-4">
                                            <div class="mb-3">
                                                <h3 class="text-lg font-semibold text-white group-hover:text-salon-gold transition-colors mb-1 line-clamp-1">
                                                    <?= htmlspecialchars($item['item_name']) ?>
                                                </h3>
                                                <?php if ($item['item_description']): ?>
                                                    <p class="text-gray-400 text-sm line-clamp-2 mb-3">
                                                        <?= htmlspecialchars($item['item_description']) ?>
                                                    </p>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Item Info -->
                                            <div class="flex items-center justify-between mb-4 text-sm">
                                                <div class="flex items-center text-gray-400">
                                                    <i class="fas fa-clock mr-1"></i>
                                                    <span><?= $item['item_duration'] ?> min</span>
                                                </div>
                                                <div class="text-salon-gold font-semibold">
                                                    <?= htmlspecialchars($item['item_category']) ?>
                                                </div>
                                            </div>

                                            <!-- Action Buttons -->
                                            <div class="flex gap-2">
                                                <button onclick="bookItem('<?= $item['item_type'] ?>', '<?= $item['item_id'] ?>', '<?= htmlspecialchars($item['item_name']) ?>', <?= $item['item_price'] ?>, <?= $item['item_duration'] ?>)"
                                                        class="flex-1 bg-salon-gold text-black py-2 px-3 rounded-lg text-sm font-semibold hover:bg-yellow-500 transition-colors">
                                                    Book Now
                                                </button>
                                                <button onclick="viewItemDetails('<?= $item['item_type'] ?>', '<?= $item['item_id'] ?>')"
                                                        class="px-3 py-2 border border-salon-gold text-salon-gold rounded-lg hover:bg-salon-gold hover:text-black transition-colors">
                                                    <i class="fas fa-info text-sm"></i>
                                                </button>
                                            </div>
                                            
                                            <!-- Added Date -->
                                            <div class="mt-3 pt-3 border-t border-secondary-700">
                                                <p class="text-xs text-gray-500">
                                                    Added <?= date('M j, Y', strtotime($item['created_at'])) ?>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Pagination -->
                            <?php if ($totalPages > 1): ?>
                                <div class="mt-12 flex justify-center">
                                    <nav class="flex items-center space-x-2">
                                        <?php if ($page > 1): ?>
                                            <a href="?page=<?= $page - 1 ?>" 
                                               class="px-3 py-2 bg-secondary-800 text-gray-300 rounded-lg hover:bg-secondary-700 transition-colors">
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                        <?php endif; ?>
                                        
                                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                            <a href="?page=<?= $i ?>" 
                                               class="px-3 py-2 <?= $i === $page ? 'bg-salon-gold text-black' : 'bg-secondary-800 text-gray-300 hover:bg-secondary-700' ?> rounded-lg transition-colors">
                                                <?= $i ?>
                                            </a>
                                        <?php endfor; ?>
                                        
                                        <?php if ($page < $totalPages): ?>
                                            <a href="?page=<?= $page + 1 ?>" 
                                               class="px-3 py-2 bg-secondary-800 text-gray-300 rounded-lg hover:bg-secondary-700 transition-colors">
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        <?php endif; ?>
                                    </nav>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>

<script>
// CSRF Token
const csrfToken = '<?= $_SESSION['csrf_token'] ?? '' ?>';

// Show toast notification with smooth animations
function showToast(message, type = 'success') {
    // Remove any existing toast first
    const existingToast = document.getElementById('wishlist-toast');
    if (existingToast) {
        existingToast.style.transform = 'translateX(100%)';
        existingToast.style.opacity = '0';
        setTimeout(() => existingToast.remove(), 200);
    }

    // Create new toast
    const toast = document.createElement('div');
    toast.id = 'wishlist-toast';

    // Set base styles for smooth animation
    toast.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 9999;
        background-color: #1e293b;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.75rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: translateX(100%) scale(0.95);
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        max-width: 20rem;
        backdrop-filter: blur(8px);
    `;

    // Set content and style based on type
    if (type === 'success') {
        toast.style.borderLeft = '4px solid #10b981';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-check-circle" style="color: #10b981; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    } else {
        toast.style.borderLeft = '4px solid #ef4444';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-exclamation-circle" style="color: #ef4444; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    }

    // Add to DOM
    document.body.appendChild(toast);

    // Trigger smooth slide-in animation
    requestAnimationFrame(() => {
        requestAnimationFrame(() => {
            toast.style.transform = 'translateX(0) scale(1)';
            toast.style.opacity = '1';
        });
    });

    // Hide toast after 2 seconds with smooth slide-out
    setTimeout(() => {
        toast.style.transform = 'translateX(100%) scale(0.95)';
        toast.style.opacity = '0';

        // Remove from DOM after animation completes
        setTimeout(() => {
            if (toast && toast.parentNode) {
                toast.remove();
            }
        }, 400);
    }, 2000);
}

// Remove item from wishlist
function removeFromWishlist(itemType, itemId) {
    if (!confirm('Are you sure you want to remove this item from your wishlist?')) {
        return;
    }

    fetch('<?= getBasePath() ?>/api/wishlist.php?action=remove', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `item_type=${itemType}&item_id=${itemId}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            // Reload page to update the grid
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');
    });
}

// Clear entire wishlist
function clearWishlist() {
    if (!confirm('Are you sure you want to clear your entire wishlist? This action cannot be undone.')) {
        return;
    }

    fetch('<?= getBasePath() ?>/api/wishlist.php?action=clear', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            // Reload page to show empty state
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');
    });
}

// Book item (redirect to booking page with pre-selected item)
function bookItem(itemType, itemId, itemName, price, duration) {
    const bookingUrl = `<?= getBasePath() ?>/customer/book?${itemType}=${itemId}`;
    window.location.href = bookingUrl;
}

// View item details (placeholder - can be expanded)
function viewItemDetails(itemType, itemId) {
    // For now, redirect to services or packages page
    if (itemType === 'service') {
        window.location.href = `<?= getBasePath() ?>/services#service-${itemId}`;
    } else {
        window.location.href = `<?= getBasePath() ?>/packages#package-${itemId}`;
    }
}

// Line clamp utility styles
const style = document.createElement('style');
style.textContent = `
    .line-clamp-1 {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
`;
document.head.appendChild(style);
</script>

<?php include __DIR__ . '/../includes/customer_footer.php'; ?>
